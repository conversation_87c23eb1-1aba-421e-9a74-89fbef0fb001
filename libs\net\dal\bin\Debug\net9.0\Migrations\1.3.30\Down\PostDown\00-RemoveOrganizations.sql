DO $$
BEGIN

DELETE FROM public."organization" WHERE "name" IN ('AF'
,'AG'
,'AudGen'
,'BC Ferries'
,'BC Housing'
,'BC Hydro'
,'BCER'
,'BCIB'
,'BCLC'
,'BCUC'
,'CFD'
,'CITZ'
,'CLBC'
,'COI'
,'DBC'
,'EAAT'
,'EAO'
,'EBC'
,'ECC'
,'ECS'
,'EMCR'
,'ENV'
,'FHA'
,'FIN'
,'FOR'
,'GCPE'
,'GPEB'
,'HLTH'
,'HMA'
,'ICBC'
,'IGRS'
,'IHA'
,'IIO'
,'INF'
,'INFBC'
,'IPC'
,'IRR'
,'JEDI'
,'LBR'
,'LCLB'
,'LDB'
,'LEDGE'
,'MC'
,'MCM'
,'NDP'
,'NHA'
,'OoO'
,'OPCC'
,'PAVCO'
,'PGT'
,'PHSA'
,'PREM'
,'PSA'
,'PSEC'
,'PSFS'
,'PSSG'
,'RCY'
,'RUBC'
,'SDPR'
,'STBC'
,'TACS'
,'TICORP'
,'TransLink'
,'TT'
,'VCHA'
,'VIHA'
,'WLRS');

END $$;
