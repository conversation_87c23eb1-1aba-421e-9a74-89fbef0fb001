{"version": 2, "dgSpecHash": "tmqYGlDK5BQ=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "libraryId": "Oracle.EntityFrameworkCore", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "libraryId": "Microsoft.Extensions.Logging.Debug", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "filePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\contentmigration\\TNO.Services.ContentMigration.csproj", "libraryId": "TNO.Services", "targetGraphs": []}]}